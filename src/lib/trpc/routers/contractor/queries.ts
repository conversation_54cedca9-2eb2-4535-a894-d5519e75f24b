import { and, asc, desc, eq, inArray, sql } from "drizzle-orm";
import type { Db } from "@/db";
import {
  address,
  bid,
  job,
  membership,
  organization,
  property,
  review,
  trade,
} from "@/db/schema";

/**
 * Contractor-specific optimized queries
 *
 * These functions provide optimized database queries specifically for the contractor router.
 * They replace ILIKE queries with full-text search and eliminate N+1 patterns.
 */

/**
 * OPTIMIZED: Replace contractor/search.ts search query
 *
 * BEFORE: ILIKE queries (slow on large datasets)
 * AFTER: Full-text search with ranking
 */
export async function getOptimizedContractorSearch(
  db: Db,
  searchQuery: string,
  excludeIds: string[] = [],
  limit = 10,
) {
  const tsQuery = searchQuery.trim().split(" ").join(" & ");

  const whereConditions = [
    sql`(
      to_tsvector('english', ${organization.name}) @@ to_tsquery('english', ${tsQuery}) OR
      to_tsvector('english', ${trade.name}) @@ to_tsquery('english', ${tsQuery}) OR
      to_tsvector('english', coalesce(${organization.description}, '')) @@ to_tsquery('english', ${tsQuery})
    )`,
  ];

  if (excludeIds.length > 0) {
    whereConditions.push(
      sql`${organization.id} NOT IN (${excludeIds.join(", ")})`,
    );
  }

  return db
    .select({
      // Organization data
      id: organization.id,
      name: organization.name,
      description: organization.description,
      imageUrl: organization.imageUrl,
      verified: organization.verified,
      createdAt: organization.createdAt,

      // Trade data
      tradeId: trade.id,
      tradeName: trade.name,

      // Address data
      addressStreet: address.street,
      addressCity: address.city,
      addressState: address.state,
      addressZip: address.zip,

      // Performance metrics
      totalJobs: sql<number>`count(distinct ${job.id})`.as("totalJobs"),
      completedJobs:
        sql<number>`count(distinct ${job.id}) filter (where ${job.status} = 'COMPLETED')`.as(
          "completedJobs",
        ),
      activeJobs:
        sql<number>`count(distinct ${job.id}) filter (where ${job.status} = 'PUBLISHED' and ${bid.status} = 'ACCEPTED')`.as(
          "activeJobs",
        ),
      totalBids: sql<number>`count(distinct ${bid.id})`.as("totalBids"),
      acceptedBids:
        sql<number>`count(distinct ${bid.id}) filter (where ${bid.status} = 'ACCEPTED')`.as(
          "acceptedBids",
        ),
      avgRating: sql<number>`avg(${review.rating})`.as("avgRating"),
      reviewCount: sql<number>`count(distinct ${review.id})`.as("reviewCount"),
      memberCount: sql<number>`count(distinct ${membership.userId})`.as(
        "memberCount",
      ),

      // Search ranking
      searchRank: sql<number>`
        ts_rank(
          to_tsvector('english', ${organization.name} || ' ' || coalesce(${organization.description}, '')),
          to_tsquery('english', ${tsQuery})
        )
      `.as("searchRank"),
    })
    .from(organization)
    .leftJoin(trade, eq(organization.tradeId, trade.id))
    .leftJoin(address, eq(organization.addressId, address.id))
    .leftJoin(membership, eq(organization.id, membership.organizationId))
    .leftJoin(
      bid,
      and(eq(organization.id, bid.organizationId), eq(bid.status, "ACCEPTED")),
    )
    .leftJoin(job, and(eq(bid.jobId, job.id), eq(job.status, "COMPLETED")))
    .leftJoin(
      review,
      and(eq(review.jobId, job.id), eq(review.reviewType, "CONTRACTOR_REVIEW")),
    )
    .where(and(...whereConditions))
    .groupBy(organization.id, trade.id, address.id)
    .orderBy(
      desc(sql`avg(${review.rating})`),
      desc(sql`ts_rank(
        to_tsvector('english', ${organization.name} || ' ' || coalesce(${organization.description}, '')),
        to_tsquery('english', ${tsQuery})
      )`),
      asc(organization.name),
    )
    .limit(limit);
}

/**
 * OPTIMIZED: Replace bids.ts listForOrganization query
 *
 * BEFORE: Complex nested relations causing N+1 queries
 * AFTER: Single optimized query with all required data
 */
export async function getOptimizedOrganizationBids(
  db: Db,
  organizationId: string,
) {
  return db
    .select({
      // Bid data
      id: bid.id,
      amount: bid.amount,
      status: bid.status,
      createdAt: bid.createdAt,

      // Job data (flattened)
      jobId: job.id,
      jobName: job.name,
      jobBudget: job.budget,
      jobStatus: job.status,
      jobStartsAt: job.startsAt,
      jobDeadline: job.deadline,

      // Property data (flattened)
      propertyName: property.name,

      // Address data (flattened)
      addressStreet: address.street,
      addressCity: address.city,
      addressState: address.state,

      // Competition data
      totalBids: sql<number>`
        (SELECT count(*) FROM ${bid} b2 WHERE b2.job_id = ${job.id})
      `.as("totalBids"),

      isLowestBid: sql<boolean>`
        ${bid.amount} = (SELECT min(amount) FROM ${bid} b3 WHERE b3.job_id = ${job.id})
      `.as("isLowestBid"),
    })
    .from(bid)
    .innerJoin(job, eq(bid.jobId, job.id))
    .innerJoin(property, eq(job.propertyId, property.id))
    .innerJoin(address, eq(property.addressId, address.id))
    .where(eq(bid.organizationId, organizationId))
    .orderBy(desc(bid.createdAt));
}

/**
 * Get organization statistics in a single query
 */
export async function getOrganizationStats(db: Db, organizationId: string) {
  const [stats] = await db
    .select({
      totalJobs: sql<number>`count(distinct ${job.id})`.as("totalJobs"),
      completedJobs:
        sql<number>`count(distinct ${job.id}) filter (where ${job.status} = 'COMPLETED')`.as(
          "completedJobs",
        ),
      activeJobs:
        sql<number>`count(distinct ${job.id}) filter (where ${job.status} = 'PUBLISHED' and ${bid.status} = 'ACCEPTED')`.as(
          "activeJobs",
        ),
      totalBids: sql<number>`count(distinct ${bid.id})`.as("totalBids"),
      acceptedBids:
        sql<number>`count(distinct ${bid.id}) filter (where ${bid.status} = 'ACCEPTED')`.as(
          "acceptedBids",
        ),
      avgRating: sql<number>`avg(${review.rating})`.as("avgRating"),
      reviewCount: sql<number>`count(distinct ${review.id})`.as("reviewCount"),
      memberCount: sql<number>`count(distinct ${membership.userId})`.as(
        "memberCount",
      ),
    })
    .from(organization)
    .leftJoin(bid, eq(organization.id, bid.organizationId))
    .leftJoin(job, eq(bid.jobId, job.id))
    .leftJoin(
      review,
      and(eq(review.jobId, job.id), eq(review.reviewType, "CONTRACTOR_REVIEW")),
    )
    .leftJoin(membership, eq(organization.id, membership.organizationId))
    .where(eq(organization.id, organizationId))
    .groupBy(organization.id);

  return stats;
}

/**
 * Get reviews for organizations with optimized query
 */
export async function getOptimizedOrganizationReviews(
  db: Db,
  organizationIds: string[],
) {
  if (organizationIds.length === 0) {
    return [];
  }

  return db
    .select({
      id: review.id,
      rating: review.rating,
      comment: review.comment,
      reviewType: review.reviewType,
      createdAt: review.createdAt,

      job: {
        id: job.id,
        name: job.name,
        completedAt: job.completedAt,
      },

      organizationId: bid.organizationId,

      // Reviewer info (property owner)
      reviewer: {
        name: sql<string>`${property.userId}`.as("reviewerName"), // This would need to be joined with user table
      },
    })
    .from(review)
    .innerJoin(job, eq(review.jobId, job.id))
    .innerJoin(
      bid,
      and(
        eq(bid.jobId, job.id),
        eq(bid.status, "ACCEPTED"),
        inArray(bid.organizationId, organizationIds),
      ),
    )
    .innerJoin(property, eq(job.propertyId, property.id))
    .where(
      and(
        eq(job.status, "COMPLETED"),
        eq(review.reviewType, "CONTRACTOR_REVIEW"),
      ),
    )
    .orderBy(desc(review.createdAt));
}
