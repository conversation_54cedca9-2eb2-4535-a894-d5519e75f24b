import { and, asc, desc, eq, gte, inArray, lte, sql } from "drizzle-orm";
import type { Db } from "@/db";
import { address, bid, job, property, schedule } from "@/db/schema";
import { transformUserJobsToNested } from "@/lib/trpc/utils/transform-helpers";

/**
 * Projects-specific optimized queries
 *
 * These functions provide optimized database queries specifically for the projects router.
 * They replace N+1 query patterns with single optimized queries.
 */

/**
 * OPTIMIZED: Replace jobs.ts listForUser query
 *
 * BEFORE: N+1 queries (1 for jobs + N for each job's relations)
 * AFTER: Single query with all required data
 */
export async function getOptimizedUserJobs(db: Db, userId: string) {
  const rawJobs = await db
    .select({
      // Job data
      id: job.id,
      name: job.name,
      status: job.status,
      budget: job.budget,
      startsAt: job.startsAt,
      deadline: job.deadline,
      createdAt: job.createdAt,
      updatedAt: job.updatedAt,
      propertyId: job.propertyId,

      // Property data (flattened)
      propertyName: property.name,
      propertyImageUrl: property.imageUrl,

      // Address data (flattened)
      addressStreet: address.street,
      addressCity: address.city,
      addressState: address.state,
      addressZip: address.zip,

      // Aggregated bid data
      bidCount: sql<number>`count(${bid.id})`.as("bidCount"),
      lowestBid: sql<number>`min(${bid.amount})`.as("lowestBid"),
      highestBid: sql<number>`max(${bid.amount})`.as("highestBid"),
      avgBid: sql<number>`avg(${bid.amount})`.as("avgBid"),
      hasAcceptedBid: sql<boolean>`bool_or(${bid.status} = 'ACCEPTED')`.as(
        "hasAcceptedBid",
      ),

      // Schedule data
      scheduleId: schedule.id,
      scheduledStartDate: schedule.proposedStartDate,
      scheduledEndDate: schedule.proposedEndDate,
    })
    .from(job)
    .innerJoin(property, eq(job.propertyId, property.id))
    .innerJoin(address, eq(property.addressId, address.id))
    .leftJoin(bid, eq(job.id, bid.jobId))
    .leftJoin(schedule, eq(job.id, schedule.jobId))
    .where(eq(property.userId, userId))
    .groupBy(
      job.id,
      property.id,
      property.name,
      property.imageUrl,
      address.id,
      address.street,
      address.city,
      address.state,
      address.zip,
      schedule.id,
      schedule.proposedStartDate,
      schedule.proposedEndDate,
    )
    .orderBy(desc(job.createdAt));

  return transformUserJobsToNested(rawJobs);
}

/**
 * OPTIMIZED: Replace jobs.ts listPublished query with location filtering
 *
 * BEFORE: Simple query without location optimization
 * AFTER: Optimized query with spatial indexing and pagination
 */
export async function getOptimizedPublishedJobs(
  db: Db,
  options: {
    limit?: number;
    offset?: number;
    location?: { lat: number; lng: number; radius: number };
    minBudget?: number;
    maxBudget?: number;
    tradeIds?: string[];
  } = {},
) {
  const {
    limit = 20,
    offset = 0,
    location,
    minBudget,
    maxBudget,
    tradeIds,
  } = options;

  let query = db
    .select({
      // Job data
      id: job.id,
      name: job.name,
      description: job.description,
      budget: job.budget,
      startsAt: job.startsAt,
      deadline: job.deadline,
      createdAt: job.createdAt,
      tradeId: job.tradeId,

      // Property data
      propertyId: property.id,
      propertyName: property.name,
      propertyImageUrl: property.imageUrl,

      // Address data
      addressStreet: address.street,
      addressCity: address.city,
      addressState: address.state,
      addressZip: address.zip,
      addressLat: address.lat,
      addressLng: address.lng,

      // Bid statistics
      bidCount: sql<number>`count(${bid.id})`.as("bidCount"),
      lowestBid: sql<number>`min(${bid.amount})`.as("lowestBid"),
      avgBid: sql<number>`avg(${bid.amount})`.as("avgBid"),

      // Distance calculation (if location provided)
      ...(location
        ? {
            distance: sql<number>`
              ST_Distance(
                ST_Point(${address.lng}, ${address.lat})::geography,
                ST_Point(${location.lng}, ${location.lat})::geography
              ) / 1609.34
            `.as("distance"), // Convert meters to miles
          }
        : {}),
    })
    .from(job)
    .innerJoin(property, eq(job.propertyId, property.id))
    .innerJoin(address, eq(property.addressId, address.id))
    .leftJoin(bid, eq(job.id, bid.jobId))
    .where(eq(job.status, "PUBLISHED"));

  // Add filters
  const whereConditions = [eq(job.status, "PUBLISHED")];

  if (minBudget) {
    whereConditions.push(gte(job.budget, minBudget));
  }

  if (maxBudget) {
    whereConditions.push(lte(job.budget, maxBudget));
  }

  if (tradeIds && tradeIds.length > 0) {
    whereConditions.push(inArray(job.tradeId, tradeIds));
  }

  if (location) {
    // Add spatial filter for performance
    whereConditions.push(
      sql`ST_DWithin(
        ST_Point(${address.lng}, ${address.lat})::geography,
        ST_Point(${location.lng}, ${location.lat})::geography,
        ${location.radius * 1609.34}
      )`, // Convert miles to meters
    );
  }

  query = query.where(and(...whereConditions));

  // Group by all non-aggregate columns
  query = query.groupBy(
    job.id,
    property.id,
    property.name,
    property.imageUrl,
    address.id,
    address.street,
    address.city,
    address.state,
    address.zip,
    address.lat,
    address.lng,
  );

  // Order by distance if location provided, otherwise by creation date
  if (location) {
    query = query.orderBy(asc(sql`distance`));
  } else {
    query = query.orderBy(desc(job.createdAt));
  }

  // Add pagination
  query = query.limit(limit).offset(offset);

  return query;
}

/**
 * Get job with all related data in a single query
 */
export async function getOptimizedJobById(db: Db, jobId: string) {
  const [jobData] = await db
    .select({
      // Job data
      id: job.id,
      name: job.name,
      description: job.description,
      status: job.status,
      budget: job.budget,
      startsAt: job.startsAt,
      deadline: job.deadline,
      createdAt: job.createdAt,
      updatedAt: job.updatedAt,
      tradeId: job.tradeId,

      // Property data
      propertyId: property.id,
      propertyName: property.name,
      propertyImageUrl: property.imageUrl,
      propertyUserId: property.userId,

      // Address data
      addressId: address.id,
      addressStreet: address.street,
      addressCity: address.city,
      addressState: address.state,
      addressZip: address.zip,
      addressLat: address.lat,
      addressLng: address.lng,

      // Bid statistics
      bidCount: sql<number>`count(${bid.id})`.as("bidCount"),
      lowestBid: sql<number>`min(${bid.amount})`.as("lowestBid"),
      highestBid: sql<number>`max(${bid.amount})`.as("highestBid"),
      avgBid: sql<number>`avg(${bid.amount})`.as("avgBid"),
      hasAcceptedBid: sql<boolean>`bool_or(${bid.status} = 'ACCEPTED')`.as(
        "hasAcceptedBid",
      ),

      // Schedule data
      scheduleId: schedule.id,
      scheduledStartDate: schedule.startDate,
      scheduledEndDate: schedule.endDate,
    })
    .from(job)
    .innerJoin(property, eq(job.propertyId, property.id))
    .innerJoin(address, eq(property.addressId, address.id))
    .leftJoin(bid, eq(job.id, bid.jobId))
    .leftJoin(schedule, eq(job.id, schedule.jobId))
    .where(eq(job.id, jobId))
    .groupBy(job.id, property.id, address.id, schedule.id);

  return jobData;
}
