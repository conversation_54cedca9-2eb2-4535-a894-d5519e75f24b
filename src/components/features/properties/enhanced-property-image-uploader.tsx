"use client";

import Uppy from "@uppy/core";
import { Dashboard } from "@uppy/react";
import { X } from "lucide-react";
import Image from "next/image";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";

// Import Uppy styles
import "@uppy/core/dist/style.min.css";
import "@uppy/dashboard/dist/style.min.css";

interface PropertyImageUploaderProps {
  initialImageUrl?: string;
  onImageUploaded: (imageUrl: string) => void;
  onImageRemoved: () => void;
}

export function PropertyImageUploader({
  initialImageUrl,
  onImageUploaded,
  onImageRemoved,
}: PropertyImageUploaderProps) {
  const [uppy, setUppy] = useState<Uppy | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string>(initialImageUrl || "");

  // Initialize Uppy when component mounts
  useEffect(() => {
    const uppyInstance = new Uppy({
      restrictions: {
        maxFileSize: 10 * 1024 * 1024, // 10MB
        maxNumberOfFiles: 1,
        allowedFileTypes: ["image/*"],
      },
    });

    setUppy(uppyInstance);

    // Set up event handlers
    uppyInstance.on("file-added", (file) => {
      console.log("File added:", file);
      // Create preview URL
      const url = URL.createObjectURL(file.data);
      setPreviewUrl(url);
      onImageUploaded(url);

      toast.success("Image selected", {
        description: "Your image has been selected successfully.",
      });
    });

    uppyInstance.on("file-removed", () => {
      setPreviewUrl("");
      onImageRemoved();
    });

    // Clean up when component unmounts
    return () => {
      uppyInstance.destroy();
    };
  }, [onImageUploaded, onImageRemoved]);

  // Update preview URL when initialImageUrl changes (for editing mode)
  useEffect(() => {
    if (initialImageUrl) {
      setPreviewUrl(initialImageUrl);
    }
  }, [initialImageUrl]);

  const handleRemoveImage = () => {
    setPreviewUrl("");
    onImageRemoved();
    if (uppy) {
      uppy.clear();
    }
  };

  return (
    <div className="space-y-4">
      {previewUrl ? (
        <div className="relative w-full rounded-lg border border-muted bg-muted/30 p-2">
          <div className="relative aspect-video w-full overflow-hidden rounded-lg">
            <Image
              src={previewUrl}
              alt="Property preview"
              fill
              className="object-cover"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              priority={!!initialImageUrl}
            />
            <Button
              type="button"
              onClick={handleRemoveImage}
              variant="destructive"
              size="icon"
              className="absolute top-2 right-2 h-8 w-8"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>
      ) : uppy ? (
        <Dashboard
          uppy={uppy}
          height={300}
          width="100%"
          showLinkToFileUploadResult={false}
          showProgressDetails={true}
          note="Images only, up to 10MB"
          proudlyDisplayPoweredByUppy={false}
        />
      ) : (
        <div className="flex h-[300px] w-full items-center justify-center rounded-lg border border-muted border-dashed">
          <p className="text-muted-foreground">Loading uploader...</p>
        </div>
      )}
    </div>
  );
}
