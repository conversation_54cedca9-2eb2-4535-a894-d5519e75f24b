"use client";

import { useQuery } from "@tanstack/react-query";
import { ClockIcon, FileTextIcon } from "lucide-react";
import { useState } from "react";
import { DashboardLoadingSkeleton } from "@/components/features";
import { ContractorDashboard } from "@/components/features/contractors/contractor-dashboard";
import { HomeownerDashboard } from "@/components/features/homeowners/homeowner-dashboard";
import { useTRPC } from "@/components/integrations/trpc/client";
import { useSession } from "@/lib/auth-client";
import {
  ActionableStats,
  generateContractorStats,
  generateHomeownerStats,
} from "./actionable-stats";
import { alertsToNotifications, NotificationsMenu } from "./notifications-menu";
import { PriorityCards } from "./priority-cards";
import {
  contractorActionConfig,
  homeownerActionConfig,
  QuickActionsMenu,
} from "./quick-actions-menu";
import {
  generateContractorAlerts,
  generateHomeownerAlerts,
} from "./smart-alerts";
import { MobileQuickStats } from "./mobile-dashboard";

export function Dashboard() {
  const trpc = useTRPC();
  const { data: session, isPending: isSessionLoading } = useSession();
  const [activeTab, setActiveTab] = useState<"overview" | "projects" | "bids">(
    "overview"
  );

  // Get user role from session
  const userRole = session?.user?.role || "homeowner";
  const userId = session?.user?.id;

  // Fetch dashboard data based on user role
  const { data: dashboardData, isLoading: isDashboardLoading } = useQuery({
    queryKey: ["dashboard", userRole, userId],
    queryFn: async () => {
      if (userRole === "contractor") {
        return trpc.dashboard.getContractorDashboard.query();
      } else {
        return trpc.dashboard.getHomeownerDashboard.query();
      }
    },
    enabled: !!session && !isSessionLoading,
  });

  // Generate alerts based on user role and dashboard data
  const alerts = dashboardData
    ? userRole === "contractor"
      ? generateContractorAlerts(dashboardData)
      : generateHomeownerAlerts(dashboardData)
    : [];

  // Convert alerts to notifications
  const notifications = alertsToNotifications(alerts);

  // Generate stats based on user role and dashboard data
  const stats = dashboardData
    ? userRole === "contractor"
      ? generateContractorStats(dashboardData)
      : generateHomeownerStats(dashboardData)
    : [];

  // Get quick actions based on user role
  const quickActions =
    userRole === "contractor" ? contractorActionConfig : homeownerActionConfig;

  // Show loading state if session or dashboard data is loading
  if (isSessionLoading || isDashboardLoading) {
    return <DashboardLoadingSkeleton />;
  }

  // Render appropriate dashboard based on user role
  return (
    <div className="space-y-6">
      {/* Mobile Quick Stats */}
      <div className="lg:hidden">
        <MobileQuickStats
          stats={stats}
          notifications={notifications.length}
          userRole={userRole}
        />
      </div>

      {/* Desktop Header with Stats and Notifications */}
      <div className="hidden items-start justify-between gap-4 lg:flex">
        <div className="flex-1">
          <h1 className="mb-1 font-bold text-3xl">Dashboard</h1>
          <p className="text-muted-foreground">
            Welcome back! Here's what's happening with your{" "}
            {userRole === "contractor" ? "business" : "projects"}.
          </p>
        </div>
        <div className="flex items-center gap-2">
          <NotificationsMenu notifications={notifications} />
          <QuickActionsMenu actions={quickActions} />
        </div>
      </div>

      {/* Priority Cards */}
      <PriorityCards
        userRole={userRole}
        dashboardData={dashboardData}
        className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3"
      />

      {/* Actionable Stats */}
      <ActionableStats stats={stats} />

      {/* Role-specific Dashboard Content */}
      {userRole === "contractor" ? (
        <ContractorDashboard data={dashboardData} />
      ) : (
        <HomeownerDashboard data={dashboardData} />
      )}

      {/* Recent Activity Section */}
      <div className="rounded-lg border bg-card p-4 shadow-sm">
        <h2 className="mb-4 font-semibold text-lg">Recent Activity</h2>
        {dashboardData?.recentActivity?.length ? (
          <div className="space-y-4">
            {dashboardData.recentActivity.map((activity, index) => (
              <div
                key={index}
                className="flex items-start gap-3 rounded-md border border-muted bg-muted/30 p-3"
              >
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary/10 text-primary">
                  {activity.type === "message" ? (
                    <FileTextIcon className="h-4 w-4" />
                  ) : (
                    <ClockIcon className="h-4 w-4" />
                  )}
                </div>
                <div>
                  <p className="font-medium">{activity.title}</p>
                  <p className="text-muted-foreground text-sm">
                    {activity.description}
                  </p>
                  <p className="mt-1 text-muted-foreground text-xs">
                    {activity.timestamp}
                  </p>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="rounded-md border border-dashed p-8 text-center">
            <p className="text-muted-foreground">No recent activity</p>
          </div>
        )}
      </div>
    </div>
  );
}