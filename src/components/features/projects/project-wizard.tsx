"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQuery } from "@tanstack/react-query";
import { format } from "date-fns";
import {
  BriefcaseIcon,
  CalendarIcon,
  CheckIcon,
  DollarSignIcon,
  ImageIcon,
  ListTodoIcon,
  MapPinIcon,
  ZapIcon,
} from "lucide-react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { TemplateSelector } from "@/components/features/projects/template-selector";
import { useTRPC } from "@/components/integrations/trpc/client";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Progress } from "@/components/ui/progress";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import type { JobTemplate } from "@/db/schema";
import { jobSchema } from "@/lib/schema";
import { cn } from "@/lib/utils";
import { JobWizardTour } from "../tours";
import { ProjectImageUploader } from "./project-image-uploader";
import { QuickHireFlow } from "./quick-hire-flow";

interface ProjectWizardProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  propertyId?: string;
}

type JobType = "STANDARD" | "QUICK_HIRE";
type WizardStep =
  | "type"
  | "basics"
  | "tasks"
  | "budget"
  | "images"
  | "review"
  | "quickHire";

const STEP_TITLES = {
  type: "Project Type",
  basics: "Basic Information",
  tasks: "Tasks & Trades",
  budget: "Budget & Timeline",
  images: "Images & Details",
  review: "Review & Create",
  quickHire: "Select Professional",
};

const STEP_DESCRIPTIONS = {
  type: "Choose the type of project you want to create",
  basics: "Tell us about your project",
  tasks: "What work needs to be done?",
  budget: "Set your budget and timeline",
  images: "Add photos and additional details",
  review: "Review your project before creating",
  quickHire: "Select a professional for your quick hire job",
};

export function ProjectWizard({
  open,
  onOpenChange,
  propertyId,
}: ProjectWizardProps) {
  const router = useRouter();
  const [jobType, setJobType] = useState<JobType | null>(null);
  const [step, setStep] = useState<WizardStep>("type");
  const [jobId, setJobId] = useState<string | null>(null);

  const trpc = useTRPC();
  const form = useForm({
    resolver: zodResolver(jobSchema),
    defaultValues: {
      name: "",
      tasks: [],
      budget: 0,
      propertyId: propertyId || "",
      startsAt: undefined,
      deadline: undefined,
      jobType: "STANDARD",
      isRecurring: false,
      recurringFrequency: null,
      images: [],
    },
  });

  const { data: properties } = useQuery(trpc.properties.list.queryOptions());

  const { data: trades } = useQuery(trpc.trades.list.queryOptions());

  const { data: quickHireTrades } = useQuery(
    trpc.trades.listAvailableForQuickHire.queryOptions(),
  );

  const mutation = useMutation(
    trpc.projects.create.mutationOptions({
      onSuccess: (data) => {
        toast.success("Project created successfully!");
        if (jobType === "QUICK_HIRE") {
          setJobId(data.id);
          setStep("quickHire");
        } else {
          handleClose();
          router.push(`/projects/${data.id}`);
        }
      },
      onError: (error) => {
        toast.error(error.message || "Failed to create project");
      },
    }),
  );

  const getStepNumber = (currentStep: WizardStep): number => {
    const steps =
      jobType === "STANDARD"
        ? ["type", "basics", "tasks", "budget", "images", "review"]
        : ["type", "basics", "tasks", "budget", "review", "quickHire"];
    return steps.indexOf(currentStep) + 1;
  };

  const getTotalSteps = (): number => {
    return jobType === "STANDARD" ? 6 : 6;
  };

  const getProgress = (): number => {
    return (getStepNumber(step) / getTotalSteps()) * 100;
  };

  const handleTypeSelect = (type: JobType) => {
    setJobType(type);
    form.setValue("jobType", type);
    setStep("basics");
  };

  const handleNext = () => {
    const currentStepNumber = getStepNumber(step);
    const steps =
      jobType === "STANDARD"
        ? ["type", "basics", "tasks", "budget", "images", "review"]
        : ["type", "basics", "tasks", "budget", "review", "quickHire"];

    if (currentStepNumber < steps.length) {
      setStep(steps[currentStepNumber] as WizardStep);
    }
  };

  const handleBack = () => {
    const currentStepNumber = getStepNumber(step);
    const steps =
      jobType === "STANDARD"
        ? ["type", "basics", "tasks", "budget", "images", "review"]
        : ["type", "basics", "tasks", "budget", "review", "quickHire"];

    if (currentStepNumber > 1) {
      setStep(steps[currentStepNumber - 2] as WizardStep);
    }
  };

  const handleClose = () => {
    setStep("type");
    setJobType(null);
    setJobId(null);
    form.reset();
    onOpenChange(false);
  };

  const applyTemplate = (template: JobTemplate) => {
    form.setValue("name", template.name);
    if (template.tasks) {
      form.setValue("tasks", template.tasks);
    }
    if (template.budget) {
      form.setValue("budget", template.budget);
    }
  };

  const addTask = () => {
    const currentTasks = form.getValues("tasks") || [];
    form.setValue("tasks", [...currentTasks, { name: "", tradeId: "" }]);
  };

  const removeTask = (index: number) => {
    const currentTasks = form.getValues("tasks") || [];
    form.setValue(
      "tasks",
      currentTasks.filter((_, i) => i !== index),
    );
  };

  const canProceed = (): boolean => {
    switch (step) {
      case "basics":
        return !!form.watch("name") && !!form.watch("propertyId");
      case "tasks": {
        const tasks = form.watch("tasks") || [];
        return (
          tasks.length > 0 && tasks.every((task) => task.name && task.tradeId)
        );
      }
      case "budget":
        return !!form.watch("budget") && form.watch("budget") > 0;
      case "images":
        return true; // Optional step
      default:
        return true;
    }
  };

  const onSubmit = async () => {
    const data = form.getValues();
    const validImages = (data.images || []).filter((img) => img.url);

    mutation.mutate({
      ...data,
      images: validImages,
    });
  };

  return (
    <>
      <JobWizardTour isOpen={open} currentStep={step} />
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-h-[90vh] max-w-4xl overflow-hidden">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {step !== "type" && (
                <div className="flex items-center gap-2 text-muted-foreground text-sm">
                  <span>
                    Step {getStepNumber(step)} of {getTotalSteps()}
                  </span>
                  <Separator orientation="vertical" className="h-4" />
                </div>
              )}
              {STEP_TITLES[step]}
            </DialogTitle>
            <DialogDescription>{STEP_DESCRIPTIONS[step]}</DialogDescription>
            {step !== "type" && (
              <Progress value={getProgress()} className="w-full" />
            )}
          </DialogHeader>

          <div className="flex-1 overflow-y-auto py-4">
            {step === "type" && (
              <div className="grid grid-cols-2 gap-6">
                <button
                  type="button"
                  className="group cursor-pointer rounded-lg border-2 border-gray-300 border-dashed p-8 text-center transition-all hover:border-orange-500 hover:bg-orange-50"
                  onClick={() => handleTypeSelect("STANDARD")}
                >
                  <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-orange-100 group-hover:bg-orange-200">
                    <BriefcaseIcon className="h-8 w-8 text-orange-600" />
                  </div>
                  <h3 className="mb-2 font-semibold text-lg">
                    Standard Project
                  </h3>
                  <p className="text-muted-foreground text-sm">
                    Create a detailed project and collect bids from multiple
                    professionals
                  </p>
                </button>

                <button
                  type="button"
                  className={`group cursor-pointer rounded-lg border-2 border-gray-300 border-dashed p-8 text-center transition-all hover:border-blue-500 hover:bg-blue-50 ${
                    !quickHireTrades?.length
                      ? "cursor-not-allowed opacity-50"
                      : ""
                  }`}
                  onClick={() =>
                    quickHireTrades?.length
                      ? handleTypeSelect("QUICK_HIRE")
                      : null
                  }
                >
                  <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-blue-100 group-hover:bg-blue-200">
                    <ZapIcon className="h-8 w-8 text-blue-600" />
                  </div>
                  <h3 className="mb-2 font-semibold text-lg">Quick Hire</h3>
                  <p className="text-muted-foreground text-sm">
                    Directly hire a professional for small or recurring jobs
                  </p>
                  {!quickHireTrades?.length && (
                    <p className="mt-2 text-red-500 text-xs">
                      No professionals available for quick hire in your area
                    </p>
                  )}
                </button>
              </div>
            )}

            {step === "basics" && (
              <Form {...form}>
                <div className="space-y-6">
                  <div className="mb-4 flex items-center gap-2">
                    <MapPinIcon className="h-5 w-5 text-orange-500" />
                    <h3 className="font-medium text-lg">Project Basics</h3>
                  </div>

                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Project Name *</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="e.g., Kitchen Remodel, Bathroom Renovation"
                            {...field}
                            className="focus-visible:ring-orange-500"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {!propertyId && (
                    <FormField
                      control={form.control}
                      name="propertyId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Property *</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            value={field.value}
                          >
                            <FormControl>
                              <SelectTrigger className="focus-visible:ring-orange-500">
                                <SelectValue placeholder="Select a property" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {properties?.map((property) => (
                                <SelectItem
                                  key={property.id}
                                  value={property.id}
                                >
                                  {property.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}

                  <div className="rounded-lg border bg-gray-50 p-4">
                    <h4 className="mb-2 font-medium">Start with a template</h4>
                    <p className="mb-3 text-muted-foreground text-sm">
                      Save time by using a pre-built project template
                    </p>
                    <TemplateSelector onSelectTemplate={applyTemplate} />
                  </div>
                </div>
              </Form>
            )}

            {step === "tasks" && (
              <Form {...form}>
                <div className="space-y-6">
                  <div className="mb-4 flex items-center gap-2">
                    <ListTodoIcon className="h-5 w-5 text-orange-500" />
                    <h3 className="font-medium text-lg">
                      What work needs to be done?
                    </h3>
                  </div>

                  <div className="space-y-4">
                    {(form.watch("tasks") || []).map((task, index) => (
                      <div
                        key={task.tradeId}
                        className="space-y-4 rounded-lg border p-4"
                      >
                        <div className="flex items-center justify-between">
                          <h4 className="font-medium">Task {index + 1}</h4>
                          {(form.watch("tasks") || []).length > 1 && (
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => removeTask(index)}
                            >
                              Remove
                            </Button>
                          )}
                        </div>

                        <div className="grid gap-4 md:grid-cols-2">
                          <FormField
                            control={form.control}
                            name={`tasks.${index}.name`}
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Task Name *</FormLabel>
                                <FormControl>
                                  <Input
                                    placeholder="e.g., Install new cabinets"
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name={`tasks.${index}.tradeId`}
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Trade *</FormLabel>
                                <Select
                                  onValueChange={field.onChange}
                                  value={field.value}
                                >
                                  <FormControl>
                                    <SelectTrigger>
                                      <SelectValue placeholder="Select trade" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    {trades?.map((trade) => (
                                      <SelectItem
                                        key={trade.id}
                                        value={trade.id}
                                      >
                                        {trade.name}
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </div>
                    ))}

                    <Button
                      type="button"
                      variant="outline"
                      onClick={addTask}
                      className="w-full"
                    >
                      Add Another Task
                    </Button>
                  </div>
                </div>
              </Form>
            )}

            {step === "budget" && (
              <Form {...form}>
                <div className="space-y-6">
                  <div className="mb-4 flex items-center gap-2">
                    <DollarSignIcon className="h-5 w-5 text-orange-500" />
                    <h3 className="font-medium text-lg">Budget & Timeline</h3>
                  </div>

                  <FormField
                    control={form.control}
                    name="budget"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Budget Amount *</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <span className="absolute top-1.5 left-3 text-muted-foreground">
                              $
                            </span>
                            <Input
                              type="number"
                              placeholder="5000"
                              {...field}
                              onChange={(e) =>
                                field.onChange(
                                  Number.parseInt(e.target.value) || 0,
                                )
                              }
                              className="pl-7 focus-visible:ring-orange-500"
                            />
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid gap-6 md:grid-cols-2">
                    <FormField
                      control={form.control}
                      name="startsAt"
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel>Start Date</FormLabel>
                          <Popover modal={true}>
                            <PopoverTrigger asChild>
                              <FormControl>
                                <Button
                                  variant="outline"
                                  className={cn(
                                    "w-full pl-3 text-left font-normal focus-visible:ring-orange-500",
                                    !field.value && "text-muted-foreground",
                                  )}
                                >
                                  {field.value ? (
                                    format(field.value, "PPP")
                                  ) : (
                                    <span>Pick a date</span>
                                  )}
                                  <CalendarIcon className="ml-auto size-4 opacity-50" />
                                </Button>
                              </FormControl>
                            </PopoverTrigger>
                            <PopoverContent
                              className="w-auto p-0"
                              align="start"
                            >
                              <Calendar
                                mode="single"
                                selected={field.value}
                                onSelect={field.onChange}
                              />
                            </PopoverContent>
                          </Popover>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="deadline"
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel>Bid Deadline</FormLabel>
                          <Popover modal={true}>
                            <PopoverTrigger asChild>
                              <FormControl>
                                <Button
                                  variant="outline"
                                  className={cn(
                                    "w-full pl-3 text-left font-normal focus-visible:ring-orange-500",
                                    !field.value && "text-muted-foreground",
                                  )}
                                >
                                  {field.value ? (
                                    format(field.value, "PPP")
                                  ) : (
                                    <span>Pick a date</span>
                                  )}
                                  <CalendarIcon className="ml-auto size-4 opacity-50" />
                                </Button>
                              </FormControl>
                            </PopoverTrigger>
                            <PopoverContent
                              className="w-auto p-0"
                              align="start"
                            >
                              <Calendar
                                mode="single"
                                selected={field.value}
                                onSelect={field.onChange}
                              />
                            </PopoverContent>
                          </Popover>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              </Form>
            )}

            {step === "images" && jobType === "STANDARD" && (
              <div className="space-y-6">
                <div className="mb-4 flex items-center gap-2">
                  <ImageIcon className="h-5 w-5 text-orange-500" />
                  <h3 className="font-medium text-lg">Images & Details</h3>
                </div>

                <p className="text-muted-foreground">
                  Add photos to help professionals understand your project
                  better. This step is optional.
                </p>

                <ProjectImageUploader
                  initialImages={form.watch("images") || []}
                  onChange={(images) => form.setValue("images", images)}
                />
              </div>
            )}

            {step === "review" && (
              <div className="space-y-6">
                <div className="mb-4 flex items-center gap-2">
                  <CheckIcon className="h-5 w-5 text-green-500" />
                  <h3 className="font-medium text-lg">Review Your Project</h3>
                </div>

                <div className="space-y-4">
                  <div className="rounded-lg border p-4">
                    <h4 className="mb-2 font-medium">Project Details</h4>
                    <p>
                      <strong>Name:</strong> {form.watch("name")}
                    </p>
                    <p>
                      <strong>Type:</strong>{" "}
                      {jobType === "STANDARD"
                        ? "Standard Project"
                        : "Quick Hire"}
                    </p>
                    <p>
                      <strong>Budget:</strong> $
                      {form.watch("budget")?.toLocaleString()}
                    </p>
                  </div>

                  <div className="rounded-lg border p-4">
                    <h4 className="mb-2 font-medium">
                      Tasks ({(form.watch("tasks") || []).length})
                    </h4>
                    {(form.watch("tasks") || []).map((task) => (
                      <div key={task.tradeId} className="mb-2">
                        <p>
                          <strong>{task.name}</strong>
                        </p>
                      </div>
                    ))}
                  </div>

                  {(form.watch("images") || []).length > 0 && (
                    <div className="rounded-lg border p-4">
                      <h4 className="mb-2 font-medium">
                        Images ({(form.watch("images") || []).length})
                      </h4>
                      <div className="grid grid-cols-4 gap-2">
                        {(form.watch("images") || []).map((image, index) => (
                          <Image
                            key={image.url}
                            src={image.url as string}
                            alt={image.description || `Image ${index + 1}`}
                            className="h-20 w-full rounded object-cover"
                          />
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {step === "quickHire" && jobId && (
              <QuickHireFlow
                projectId={jobId}
                tradeId={(form.watch("tasks") || [])[0]?.tradeId || ""}
                onComplete={handleClose}
              />
            )}
          </div>

          <div className="flex justify-between border-t pt-4">
            <div>
              {step !== "type" && step !== "quickHire" && (
                <Button variant="outline" onClick={handleBack}>
                  Back
                </Button>
              )}
            </div>

            <div className="flex gap-2">
              <Button variant="outline" onClick={handleClose}>
                Cancel
              </Button>

              {step === "review" ? (
                <Button
                  onClick={onSubmit}
                  variant="tc_orange"
                  disabled={mutation.isPending}
                >
                  {mutation.isPending
                    ? "Creating..."
                    : jobType === "STANDARD"
                      ? "Create Project"
                      : "Continue to Select Professional"}
                </Button>
              ) : step !== "type" && step !== "quickHire" ? (
                <Button
                  onClick={handleNext}
                  variant="tc_orange"
                  disabled={!canProceed()}
                >
                  Next
                </Button>
              ) : null}
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
